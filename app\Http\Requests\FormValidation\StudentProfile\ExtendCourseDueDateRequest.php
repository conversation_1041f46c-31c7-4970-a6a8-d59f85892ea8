<?php

namespace App\Http\Requests;

namespace App\Http\Requests\FormValidation\StudentProfile;

use App\DTO\studentProfile\ExtendCourseDueDateDTO;
use App\Traits\ResponseTrait;
use Illuminate\Foundation\Http\FormRequest;
use Support\Contracts\HasDTO;
use Support\Traits\CommonTrait;

class ExtendCourseDueDateRequest extends FormRequest implements HasDTO
{
    use CommonTrait;
    use ResponseTrait;

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            // 'college_id' => ['required', 'integer', 'exists:rto_colleges,id'],
            // 'student_id' => ['required', 'integer', 'exists:rto_students,id'],
            // 'selectedStudCourseIDForExtend' => ['sometimes', 'integer', 'exists:rto_student_courses,id'],
            // 'extend_course_duration_type' => ['required', 'integer', Rule::in([1, 2, 3, 4])], // 1=Day, 2=Week, 3=Month, 4=Year
            'student_course_id' => 'required', // 'integer', 'exists:rto_student_courses,id',
            'extend_finish_date' => 'required',
            'extension_reason' => 'required', // 'string', 'min:10', 'max:500'
            // 'extend_total_weeks' => 'required',
        ];
    }

    public function withValidator($validator)
    {
        if (! $validator->fails()) {
            $this->merge([
                'extend_finish_date' => $this->convertDateFormatV2($this->input('extend_finish_date')),
            ]);
        }
    }

    public function DTO()
    {
        return ExtendCourseDueDateDTO::LazyFromArray($this->input());
    }
}
