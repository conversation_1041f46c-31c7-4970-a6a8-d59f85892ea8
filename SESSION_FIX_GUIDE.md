# Session Issue Fix Guide - Galaxy360 Timetable

## Problem Summary
Session data stored in `store_course_and_unit` action is not available in `getTimetableStatusData` method on local environment, but works fine on server.

## Root Cause Analysis

The issue is related to **Sanctum session handling** in API routes. Your API routes use Sanctum authentication which handles sessions differently than regular web routes.

### Key Findings:
1. Both methods are in API routes (`routes/api.php`) with Sanctum middleware
2. API routes use `EnsureFrontendRequestsAreStateful` middleware for session handling
3. Local environment may have different Sanctum configuration than server

## Immediate Solutions

### Solution 1: Check Sanctum Configuration

1. **Verify your `.env` file contains:**
```env
SANCTUM_STATEFUL_DOMAINS=localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
```

2. **Add your local domain to Sanctum stateful domains:**
If you're using a custom local domain (like `galaxy360.local`), add it to `SANCTUM_STATEFUL_DOMAINS`.

### Solution 2: Frontend Request Headers

Ensure your frontend requests include these headers:
```javascript
headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json',
    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
}
```

### Solution 3: Clear Cache and Restart

Run these commands:
```bash
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
```

Then restart your local server.

## Advanced Solutions

### Solution 4: Alternative Session Storage

If the issue persists, try using database sessions:

1. **Create sessions table:**
```bash
php artisan session:table
php artisan migrate
```

2. **Update .env:**
```env
SESSION_DRIVER=database
```

### Solution 5: Add Session Debugging

I've added comprehensive debugging to your controller. Check `storage/logs/laravel.log` for:
- Session IDs
- Session data
- Request headers
- Sanctum configuration

### Solution 6: Temporary Workaround

If you need an immediate fix, you can store the data in cache instead of session:

```php
// In store_course_and_unit
Cache::put('course_and_unit_' . auth()->id(), $data, 3600); // 1 hour

// In getTimetableStatusData
$getCousreAndUnitDetails = Cache::get('course_and_unit_' . auth()->id());
```

## Testing Steps

1. **Run the debug script:**
```bash
php debug_session.php
```

2. **Test the session flow:**
   - Call `store_course_and_unit` action
   - Immediately call `getTimetableStatusData`
   - Check logs for session ID consistency

3. **Browser testing:**
   - Open browser developer tools
   - Check Network tab for session cookies
   - Verify cookies are sent with both requests

## Common Issues & Fixes

### Issue 1: Different Session IDs
**Cause:** Requests coming from different domains or missing cookies
**Fix:** Ensure all requests use the same domain and include cookies

### Issue 2: CSRF Token Mismatch
**Cause:** Missing or invalid CSRF token
**Fix:** Include proper CSRF token in request headers

### Issue 3: Sanctum Domain Mismatch
**Cause:** Local domain not in SANCTUM_STATEFUL_DOMAINS
**Fix:** Add your local domain to the configuration

### Issue 4: Session Driver Issues
**Cause:** File permissions or storage issues
**Fix:** Switch to database sessions or fix file permissions

## Monitoring

The debug code I added will log:
- Session configuration
- Request details
- Session data before and after retrieval
- Sanctum domain matching

Check `storage/logs/laravel.log` for these entries.

## Next Steps

1. Run the debug script to identify configuration issues
2. Check the logs after testing the session flow
3. Apply the appropriate solution based on findings
4. Remove debug code once issue is resolved

## Files Modified

- `app/Http/Controllers/v2/api/TimeTableApiController.php` - Added debugging
- `debug_session.php` - Debug script (can be deleted after fixing)
- `SESSION_FIX_GUIDE.md` - This guide (can be deleted after fixing)
