<?php
/**
 * Simple test script to verify session functionality is working
 */

echo "=== Testing Session Fix ===\n\n";

// Check .env file directly
echo "1. CHECKING .ENV CONFIGURATION:\n";
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);

    // Check for session configurations
    if (preg_match("/^SANCTUM_STATEFUL_DOMAINS=(.*)$/m", $envContent, $matches)) {
        echo "   ✅ SANCTUM_STATEFUL_DOMAINS: " . trim($matches[1]) . "\n";
    } else {
        echo "   ❌ SANCTUM_STATEFUL_DOMAINS: NOT SET\n";
    }

    if (preg_match("/^SESSION_DRIVER=(.*)$/m", $envContent, $matches)) {
        echo "   ✅ SESSION_DRIVER: " . trim($matches[1]) . "\n";
    } else {
        echo "   ❌ SESSION_DRIVER: NOT SET\n";
    }
} else {
    echo "   ❌ .env file not found!\n";
}
echo "\n";

// Test session storage
echo "2. SESSION STORAGE TEST:\n";
$sessionPath = __DIR__ . '/storage/framework/sessions';
echo "   Session Path: $sessionPath\n";
echo "   Path Exists: " . (is_dir($sessionPath) ? 'Yes' : 'No') . "\n";
if (is_dir($sessionPath)) {
    echo "   Path Writable: " . (is_writable($sessionPath) ? 'Yes' : 'No') . "\n";
} else {
    echo "   ❌ Session directory does not exist!\n";
}
echo "\n";

echo "3. MIDDLEWARE CHECK:\n";
$kernelFile = __DIR__ . '/app/Http/Kernel.php';
if (file_exists($kernelFile)) {
    $kernelContent = file_get_contents($kernelFile);

    // Check for session middleware
    if (strpos($kernelContent, 'StartSession::class') !== false && strpos($kernelContent, '// \Illuminate\Session\Middleware\StartSession::class') === false) {
        echo "   ✅ StartSession middleware is ENABLED\n";
    } else {
        echo "   ❌ StartSession middleware is DISABLED or COMMENTED\n";
    }

    // Check for Sanctum middleware
    if (strpos($kernelContent, 'EnsureFrontendRequestsAreStateful::class') !== false) {
        echo "   ✅ Sanctum stateful middleware found\n";
    } else {
        echo "   ❌ Sanctum stateful middleware not found\n";
    }
} else {
    echo "   ❌ Kernel.php not found\n";
}

echo "\n4. EXPECTED RESULT:\n";
echo "   With the session middleware now enabled, your timetable functionality should work properly.\n";
echo "   The session data stored in 'store_course_and_unit' should now be available in 'getTimetableStatusData'.\n\n";

echo "5. NEXT STEPS:\n";
echo "   - Test your timetable functionality\n";
echo "   - If it works, you can delete this test file\n";
echo "   - If it still doesn't work, check the Laravel logs for any errors\n\n";

echo "=== Test Complete ===\n";
