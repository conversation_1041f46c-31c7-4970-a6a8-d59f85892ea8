<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class StudentCourseExtensionHistory extends Model
{
    use LogsActivity;

    protected $table = 'rto_course_extension_history';

    protected $fillable = [
        'id',
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'start_date',
        'finish_date',
        'new_finish_date',
        'total_weeks',
        'new_total_weeks',
        'extension_reason',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
    ];

    protected $logAttributes = [
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'start_date',
        'finish_date',
        'new_finish_date',
        'total_weeks',
        'new_total_weeks',
        'extension_reason',
    ];

    public function getGalaxyLogNameAttribute(): string
    {
        return implode('-', [@$this->student->generated_stud_id, @$this->course->course_code]);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => ":subject.galaxy_log_name Student course extend history has been {$eventName}");
    }

    public function student()
    {
        return $this->hasOne(Student::class, 'id', 'student_id');
    }

    public function course()
    {
        return $this->hasOne(Courses::class, 'id', 'course_id');
    }
    // public function tapActivity(Activity $activity, string $eventName)
    // {
    //     $activity->log_name = (new self)->getMorphClass() ;
    //     $activity->description = "Student course status history $eventName";

    // }
}
