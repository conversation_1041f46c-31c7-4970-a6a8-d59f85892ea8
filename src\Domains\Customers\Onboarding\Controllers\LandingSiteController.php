<?php

namespace Domains\Customers\Onboarding\Controllers;

use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Support\DTO\MailAddress;
use Support\Mails\EmailService;

class LandingSiteController
{
    use ValidatesRequests;

    public function home()
    {
        $registerUrl = config('features.onboarding') ? url('onboarding/register') : url('contact');

        return view('landing.home', compact('registerUrl'));
    }

    public function about()
    {
        return view('landing.about');
    }

    public function pricing()
    {
        return view('landing.pricing');
    }

    public function faq()
    {
        return view('landing.faq');
    }

    public function features()
    {
        return view('landing.features');
    }

    public function contact()
    {
        $registerUrl = config('features.onboarding') ? url('onboarding/register') : url('contact');

        return view('landing.contact', compact('registerUrl'));
    }

    public function privacyPolicy()
    {
        return view('landing.terms');
    }

    public function termsCondition()
    {
        return view('landing.terms');
    }

    public function cookiePolicy()
    {
        return view('landing.cookie-policy');
    }

    public function postContact(Request $request)
    {
        $this->validate($request, [
            'name' => 'required',
            'email' => 'required|email',
            'subject' => 'required',
            'message' => 'required',
        ]);

        (new EmailService)
            ->to(new MailAddress($request->email))
            ->subject($request->subject)
            ->send('mail.onboarding.contact', [
                'input' => $request->all(),
            ]);

        return redirect()->back()->with(['success' => 'Message Sent']);
    }
}
